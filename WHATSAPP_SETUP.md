# WhatsApp AI Bot Setup

This project now includes a WhatsApp AI bot powered by <PERSON><PERSON> and <PERSON><PERSON>. The bot can participate in both group chats and private conversations.

## Features

- 🤖 AI-powered responses using Google's Gemini model
- 💾 Persistent conversation memory across sessions
- 👥 Support for both group and private chats
- 🔄 Automatic reconnection on disconnects
- 📱 QR code authentication for WhatsApp Web

## Prerequisites

1. **Google API Key**: You need a Google API key for the Gemini model
2. **Node.js**: Version 20.9.0 or higher
3. **WhatsApp Account**: A WhatsApp account for the bot

## Setup Instructions

### 1. Environment Configuration

Create a `.env` file in the project root and add your Google API key:

```bash
GOOGLE_API_KEY=your_google_api_key_here
```

### 2. Install Dependencies

Dependencies are already installed, but if you need to reinstall:

```bash
npm install --legacy-peer-deps
```

### 3. Start the WhatsApp Bot

Run the bot using one of these commands:

```bash
# Production mode
npm run whatsapp

# Development mode (with auto-restart)
npm run whatsapp:dev
```

### 4. Authentication

1. When you start the bot, it will display a QR code in the terminal
2. Open WhatsApp on your phone
3. Go to Settings > Linked Devices > Link a Device
4. Scan the QR code displayed in the terminal
5. The bot will connect and start responding to messages

## How It Works

### Message Processing

The bot processes incoming messages and:

1. **Filters Messages**: Ignores status broadcasts and self-messages
2. **Extracts Content**: Handles text messages, images with captions, etc.
3. **AI Processing**: Uses the Mastra WhatsApp agent to generate responses
4. **Memory**: Remembers conversation context and user preferences
5. **Response**: Sends AI-generated replies back to WhatsApp

### Memory System

The bot uses Mastra's memory system to:

- Remember user preferences and conversation history
- Maintain context across multiple conversations
- Store user profiles and interaction patterns
- Provide personalized responses based on past interactions

### Agent Configuration

The WhatsApp agent is configured to:

- Keep responses concise for messaging
- Be helpful and friendly
- Respect group chat dynamics
- Remember user context and preferences

## File Structure

```
src/
├── whatsapp/
│   ├── whatsapp-bot.ts      # Main WhatsApp bot implementation
│   └── index.ts             # Bot startup and management
├── mastra/
│   ├── agents/
│   │   ├── whatsapp-agent.ts # WhatsApp-specific AI agent
│   │   └── chatbot-agent.ts  # Original chatbot agent
│   └── index.ts             # Mastra configuration
└── start-whatsapp-bot.ts    # Bot startup script
```

## Customization

### Modifying Bot Behavior

Edit `src/mastra/agents/whatsapp-agent.ts` to customize:

- Response style and tone
- Instructions and behavior
- Memory configuration
- Tool integrations

### Adding Features

You can extend the bot by:

1. Adding tools to the agent (web search, APIs, etc.)
2. Implementing message filtering logic
3. Adding support for different message types
4. Integrating with external services

## Troubleshooting

### Common Issues

1. **QR Code Not Displaying**: Make sure your terminal supports Unicode characters
2. **Connection Errors**: Check your internet connection and WhatsApp Web status
3. **API Errors**: Verify your Google API key is correct and has Gemini access
4. **Memory Issues**: Check that the SQLite database is writable

### Logs

The bot uses Pino logging. Check the console output for detailed information about:

- Connection status
- Message processing
- Error details
- AI agent responses

### Authentication Files

The bot stores authentication data in `./auth_info_baileys/` directory. This allows the bot to reconnect without re-scanning the QR code. Keep this directory secure and don't commit it to version control.

## Security Notes

- Keep your Google API key secure
- Don't commit authentication files to version control
- Be mindful of message content when using AI processing
- Consider rate limiting for production use

## Support

For issues related to:

- **Baileys**: Check the [Baileys documentation](https://github.com/WhiskeySockets/Baileys)
- **Mastra**: Check the [Mastra documentation](https://mastra.ai)
- **Google AI**: Check the [Google AI documentation](https://ai.google.dev)
