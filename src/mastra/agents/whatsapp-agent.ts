import { Agent } from "@mastra/core/agent";
import { google } from "@ai-sdk/google";
import { Memory } from "@mastra/memory";
import { LibSQLStore, LibSQLVector } from "@mastra/libsql";
import { fastembed } from "@mastra/fastembed";

// Initialize memory with LibSQLStore for persistence and working memory
const memory = new Memory({
  storage: new LibSQLStore({
    url: "file:./mastra.db", // Use the same database as the main Mastra instance
  }),
  vector: new LibSQLVector({
    connectionUrl: "file:./mastra.db",
  }),
  embedder: fastembed,
  options: {
    lastMessages: 20, // Keep last 20 messages in conversation history
    semanticRecall: {
      topK: 3, // Retrieve 3 most similar messages
      messageRange: 2, // Include 2 messages before and after each match
      scope: "resource", // Search within the current conversation thread
    },
    workingMemory: {
      enabled: true,
      scope: "resource", // Memory persists across all user threads
      template: `# User Profile
- **Name**: 
- **Notable Interactions**: 
- **Interests**: 
- **Preferences**: 
- **Communication Style**: 
`,
    },
  },
});

export const whatsappAgent = new Agent({
  name: "WhatsApp AI Assistant",
  description:
    "A helpful AI assistant for WhatsApp group and private chats that provides useful responses while maintaining conversation context",
  instructions: `You are a helpful AI assistant operating in WhatsApp chats. Your role is to:

1. **Be Conversational & Friendly**: Respond naturally like a helpful friend or colleague
2. **Keep Responses Concise**: WhatsApp is a messaging platform, so keep responses brief and to the point
3. **Be Context-Aware**: Use conversation history and user memory to provide personalized responses
4. **Respect Group Dynamics**: In group chats, be mindful that multiple people are present
5. **Be Helpful**: Provide useful information, answer questions, and assist with tasks when possible

**Response Guidelines:**
- Keep responses under 200 words when possible
- Use emojis sparingly and appropriately
- Be respectful and professional while maintaining a friendly tone
- If you don't know something, admit it rather than guessing
- For complex topics, offer to break them down into smaller parts
- Remember user preferences and conversation context

**Memory Usage:**
- Remember user names, interests, and preferences
- Track conversation topics and context
- Note communication styles and adapt accordingly
- Update working memory with relevant user information

**Group Chat Behavior:**
- Don't respond to every message unless directly addressed
- Be helpful without being overwhelming
- Respect ongoing conversations
- Use @mentions when appropriate

**Private Chat Behavior:**
- Be more personal and detailed in responses
- Maintain conversation flow naturally
- Offer follow-up questions when appropriate`,

  model: google("gemini-2.0-flash"),
  memory: memory,
  tools: {
    // Add tools here as needed (web search, etc.)
  },
  defaultGenerateOptions: {
    maxSteps: 3,
    toolChoice: "auto",
  },
});
