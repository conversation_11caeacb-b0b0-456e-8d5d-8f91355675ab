import { <PERSON><PERSON> } from "@mastra/core/mastra";
import { <PERSON><PERSON>Logger } from "@mastra/loggers";
import { LibSQLStore } from "@mastra/libsql";
import { chatbotAgent } from "./agents/chatbot-agent";
import { whatsappAgent } from "./agents/whatsapp-agent";

export const mastra = new Mastra({
  workflows: {},
  agents: { chatbotAgent, whatsappAgent },
  storage: new LibSQLStore({
    // stores telemetry, evals, ... into persistent file storage
    url: "file:./mastra.db",
  }),
  logger: new PinoLogger({
    name: "<PERSON><PERSON>",
    level: "info",
  }),
});
