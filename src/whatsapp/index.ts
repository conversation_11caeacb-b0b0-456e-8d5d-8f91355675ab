import { WhatsAppBot } from './whatsapp-bot';

export { WhatsAppBot };

// Main function to start the bot
export async function startWhatsAppBot() {
  const bot = new WhatsAppBot();
  
  try {
    await bot.start();
    console.log('WhatsApp bot is running...');
    
    // Handle graceful shutdown
    process.on('SIGINT', async () => {
      console.log('\nShutting down WhatsApp bot...');
      await bot.stop();
      process.exit(0);
    });
    
    process.on('SIGTERM', async () => {
      console.log('\nShutting down WhatsApp bot...');
      await bot.stop();
      process.exit(0);
    });
    
  } catch (error) {
    console.error('Failed to start WhatsApp bot:', error);
    process.exit(1);
  }
}

// Auto-start if this file is run directly
if (import.meta.url === `file://${process.argv[1]}`) {
  startWhatsAppBot();
}
