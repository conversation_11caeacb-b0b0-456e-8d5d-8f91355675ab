import makeWASocket, {
  ConnectionState,
  DisconnectReason,
  useMultiFileAuthState,
  WAMessage,
  WASocket,
  proto,
  isJidGroup,
  extractMessageContent,
  getContentType,
} from "@whiskeysockets/baileys";
import { Boom } from "@hapi/boom";
import { mastra } from "../mastra";
import { PinoLogger } from "@mastra/loggers";

export class WhatsAppBot {
  private socket: WASocket | null = null;
  private logger: PinoLogger;

  constructor() {
    this.logger = new PinoLogger({
      name: "WhatsAppBot",
      level: "info",
    });
  }

  async start() {
    try {
      // Use multi-file auth state for persistent sessions
      const { state, saveCreds } = await useMultiFileAuthState(
        "./auth_info_baileys"
      );

      this.socket = makeWASocket({
        auth: state,
        printQRInTerminal: true,
        logger: this.logger as any,
        browser: ["Personal AI Bot", "Chrome", "1.0.0"],
      });

      // Handle connection updates
      this.socket.ev.on("connection.update", (update) => {
        this.handleConnectionUpdate(update);
      });

      // Save credentials when updated
      this.socket.ev.on("creds.update", saveCreds);

      // Handle incoming messages
      this.socket.ev.on("messages.upsert", async (m) => {
        await this.handleMessages(m.messages);
      });

      this.logger.info("WhatsApp bot started successfully");
    } catch (error) {
      this.logger.error("Failed to start WhatsApp bot:", error);
      throw error;
    }
  }

  private handleConnectionUpdate(update: Partial<ConnectionState>) {
    const { connection, lastDisconnect } = update;

    if (connection === "close") {
      const shouldReconnect =
        (lastDisconnect?.error as Boom)?.output?.statusCode !==
        DisconnectReason.loggedOut;

      this.logger.info("Connection closed due to:", lastDisconnect?.error);

      if (shouldReconnect) {
        this.logger.info("Reconnecting...");
        this.start();
      }
    } else if (connection === "open") {
      this.logger.info("WhatsApp connection opened successfully");
    }
  }

  private async handleMessages(messages: WAMessage[]) {
    for (const message of messages) {
      try {
        // Skip if message is from status broadcast
        if (message.key.remoteJid === "status@broadcast") continue;

        // Skip if message is from self
        if (message.key.fromMe) continue;

        // Extract message content
        const messageContent = this.extractMessageText(message);
        if (!messageContent) continue;

        const isGroup = isJidGroup(message.key.remoteJid!);
        const chatId = message.key.remoteJid!;
        const senderId = message.key.participant || message.key.remoteJid!;

        this.logger.info(
          `Received message from ${senderId} in ${isGroup ? "group" : "private"}: ${messageContent}`
        );

        // Process message with Mastra agent
        await this.processMessage(messageContent, chatId, senderId, isGroup);
      } catch (error) {
        this.logger.error("Error handling message:", error);
      }
    }
  }

  private extractMessageText(message: WAMessage): string | null {
    const content = extractMessageContent(message.message);
    if (!content) return null;

    const messageType = getContentType(content);

    switch (messageType) {
      case "conversation":
        return content.conversation || null;
      case "extendedTextMessage":
        return content.extendedTextMessage?.text || null;
      case "imageMessage":
        return content.imageMessage?.caption || null;
      case "videoMessage":
        return content.videoMessage?.caption || null;
      default:
        return null;
    }
  }

  private async processMessage(
    messageText: string,
    chatId: string,
    senderId: string,
    isGroup: boolean
  ) {
    try {
      // Use Mastra WhatsApp agent to generate response
      const response = await mastra.agent("whatsappAgent").generate(
        [
          {
            role: "user",
            content: messageText,
          },
        ],
        {
          resourceId: chatId, // Use chat ID as resource for memory persistence
        }
      );

      const botResponse = response.text;

      // Send response back to WhatsApp
      await this.sendMessage(chatId, botResponse);

      this.logger.info(`Sent response to ${chatId}: ${botResponse}`);
    } catch (error) {
      this.logger.error("Error processing message with Mastra:", error);

      // Send error message
      await this.sendMessage(
        chatId,
        "Sorry, I encountered an error processing your message. Please try again."
      );
    }
  }

  async sendMessage(chatId: string, text: string) {
    if (!this.socket) {
      throw new Error("WhatsApp socket not initialized");
    }

    try {
      await this.socket.sendMessage(chatId, { text });
    } catch (error) {
      this.logger.error("Error sending message:", error);
      throw error;
    }
  }

  async stop() {
    if (this.socket) {
      await this.socket.logout();
      this.socket = null;
      this.logger.info("WhatsApp bot stopped");
    }
  }
}
